#!/usr/bin/env python3
"""
Simple test to verify WebSocket proxy works
"""

import asyncio
import websockets
import json

async def test_proxy():
    """Test WebSocket connection through FastAPI proxy"""
    print("Testing WebSocket proxy connection...")
    
    uri = "ws://localhost:8000/ws/jambonz/123"
    
    try:
        print(f"Connecting to: {uri}")
        async with websockets.connect(
            uri,
            subprotocols=["ws.jambonz.org"]
        ) as websocket:
            print("✅ Connected successfully!")
            print(f"Subprotocol: {websocket.subprotocol}")
            
            # Send a simple test message
            test_msg = {"type": "test", "data": "hello"}
            await websocket.send(json.dumps(test_msg))
            print("✅ Message sent")
            
            # Try to receive a response (with short timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                print(f"✅ Response: {response}")
            except asyncio.TimeoutError:
                print("⚠️  No response (timeout)")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Error type: {type(e).__name__}")

if __name__ == "__main__":
    asyncio.run(test_proxy())
