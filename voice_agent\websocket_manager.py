import asyncio
import json
import logging
import time
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Set, Callable, List
from dataclasses import dataclass, field
from enum import Enum
import websockets
from websockets.exceptions import ConnectionClosed, WebSocketException
from websockets.server import WebSocketServerProtocol
from websockets.http import Headers
from websockets.http11 import Response
import http
import os
from contextlib import asynccontextmanager

# Module-local logger
logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """WebSocket connection states"""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    RECONNECTING = "reconnecting"
    FAILED = "failed"
    CLOSED = "closed"

@dataclass
class ConnectionMetrics:
    """Metrics for monitoring WebSocket connections"""
    connection_id: str
    call_sid: Optional[str] = None
    hospital_id: Optional[str] = None
    connected_at: Optional[datetime] = None
    last_message_at: Optional[datetime] = None
    messages_sent: int = 0
    messages_received: int = 0
    reconnection_count: int = 0
    total_downtime: float = 0.0  # seconds
    last_error: Optional[str] = None
    
    def update_message_sent(self):
        """Update sent message metrics"""
        self.messages_sent += 1
        self.last_message_at = datetime.now()
    
    def update_message_received(self):
        """Update received message metrics"""
        self.messages_received += 1
        self.last_message_at = datetime.now()

@dataclass
class WebSocketConnection:
    """Represents a WebSocket connection to Jambonz"""
    connection_id: str
    websocket: Optional[websockets.WebSocketServerProtocol] = None
    state: ConnectionState = ConnectionState.DISCONNECTED
    call_sid: Optional[str] = None
    hospital_id: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    last_heartbeat: Optional[datetime] = None
    pending_acks: Set[str] = field(default_factory=set)
    message_queue: asyncio.Queue = field(default_factory=asyncio.Queue)
    metrics: ConnectionMetrics = field(init=False)
    
    def __post_init__(self):
        """Initialize metrics after dataclass creation"""
        self.metrics = ConnectionMetrics(
            connection_id=self.connection_id,
            call_sid=self.call_sid,
            hospital_id=self.hospital_id
        )
    
    @property
    def is_connected(self) -> bool:
        """Check if connection is active"""
        if self.websocket is None or self.state != ConnectionState.CONNECTED:
            return False

        # Check if websocket is closed using proper websockets 15.x API
        try:
            # In websockets 15.x, check the close_code attribute
            return not hasattr(self.websocket, 'close_code') or self.websocket.close_code is None
        except AttributeError:
            # Fallback for older versions or different websocket implementations
            try:
                return not getattr(self.websocket, 'closed', False)
            except:
                # If all else fails, assume connected if websocket exists
                return True
    
    async def send_message(self, message: Dict[str, Any]) -> bool:
        """
        Send message through WebSocket connection
        
        Args:
            message: Message to send
            
        Returns:
            bool: Success status
        """
        if not self.is_connected:
            logger.warning(f"Cannot send message on disconnected connection {self.connection_id}")
            return False
        
        try:
            message_str = json.dumps(message)
            await self.websocket.send(message_str)
            self.metrics.update_message_sent()
            logger.debug(f"Sent message on connection {self.connection_id}: {message.get('type', 'unknown')}")
            return True
        except Exception as e:
            logger.error(f"Error sending message on connection {self.connection_id}: {e}")
            self.state = ConnectionState.FAILED
            return False
    
    async def close(self, code: int = 1000, reason: str = "Normal closure"):
        """
        Close WebSocket connection gracefully
        
        Args:
            code: WebSocket close code
            reason: Close reason
        """
        if self.websocket:
            try:
                # Check if websocket is already closed using proper websockets 15.x API
                is_closed = False
                try:
                    # In websockets 15.x, check the close_code attribute
                    is_closed = hasattr(self.websocket, 'close_code') and self.websocket.close_code is not None
                except AttributeError:
                    # Fallback for older versions
                    is_closed = getattr(self.websocket, 'closed', False)

                if not is_closed:
                    await self.websocket.close(code=code, reason=reason)
                    logger.info(f"Closed connection {self.connection_id}: {reason}")
                else:
                    logger.debug(f"Connection {self.connection_id} already closed")
            except Exception as e:
                logger.error(f"Error closing connection {self.connection_id}: {e}")
        
        self.state = ConnectionState.CLOSED
        self.websocket = None

class ReconnectionManager:
    """Manages WebSocket reconnection logic with exponential backoff"""
    
    def __init__(self, max_retries: int = 5, base_delay: float = 1.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
    
    def calculate_delay(self, attempt: int) -> float:
        """Calculate exponential backoff delay"""
        delay = self.base_delay * (2 ** attempt)
        return min(delay, self.max_delay)
    
    async def should_reconnect(self, connection: WebSocketConnection) -> bool:
        """Determine if reconnection should be attempted"""
        return (
            connection.metrics.reconnection_count < self.max_retries
            and connection.state != ConnectionState.CLOSED
        )

class ConnectionPool:
    """Pool for managing multiple WebSocket connections"""
    
    def __init__(self, max_connections: int = 1000):
        self.max_connections = max_connections
        self.connections: Dict[str, WebSocketConnection] = {}
        self.call_to_connection: Dict[str, str] = {}  # call_sid -> connection_id
        self.hospital_connections: Dict[str, Set[str]] = {}  # hospital_id -> connection_ids
        self._lock = asyncio.Lock()
    
    async def add_connection(self, connection: WebSocketConnection) -> bool:
        """Add connection to pool"""
        async with self._lock:
            if len(self.connections) >= self.max_connections:
                logger.warning(f"Connection pool full, rejecting connection {connection.connection_id}")
                return False
            
            self.connections[connection.connection_id] = connection
            
            if connection.call_sid:
                self.call_to_connection[connection.call_sid] = connection.connection_id
            
            if connection.hospital_id:
                if connection.hospital_id not in self.hospital_connections:
                    self.hospital_connections[connection.hospital_id] = set()
                self.hospital_connections[connection.hospital_id].add(connection.connection_id)
            
            logger.info(f"Added connection {connection.connection_id} to pool")
            return True
    
    async def remove_connection(self, connection_id: str) -> Optional[WebSocketConnection]:
        """Remove connection from pool"""
        async with self._lock:
            connection = self.connections.pop(connection_id, None)
            if not connection:
                return None
            
            # Clean up mappings
            if connection.call_sid and connection.call_sid in self.call_to_connection:
                del self.call_to_connection[connection.call_sid]
            
            if connection.hospital_id and connection.hospital_id in self.hospital_connections:
                self.hospital_connections[connection.hospital_id].discard(connection_id)
                if not self.hospital_connections[connection.hospital_id]:
                    del self.hospital_connections[connection.hospital_id]
            
            logger.info(f"Removed connection {connection_id} from pool")
            return connection
    
    async def get_connection(self, connection_id: str) -> Optional[WebSocketConnection]:
        """Get connection by ID"""
        async with self._lock:
            return self.connections.get(connection_id)
    
    async def get_connection_by_call(self, call_sid: str) -> Optional[WebSocketConnection]:
        """Get connection by call SID"""
        async with self._lock:
            connection_id = self.call_to_connection.get(call_sid)
            if connection_id:
                return self.connections.get(connection_id)
            return None
    
    async def get_hospital_connections(self, hospital_id: str) -> List[WebSocketConnection]:
        """Get all connections for a hospital"""
        async with self._lock:
            connection_ids = self.hospital_connections.get(hospital_id, set())
            return [self.connections[cid] for cid in connection_ids if cid in self.connections]
    
    async def get_all_connections(self) -> List[WebSocketConnection]:
        """Get all active connections"""
        async with self._lock:
            return list(self.connections.values())
    
    async def cleanup_stale_connections(self, max_age: timedelta = timedelta(hours=1)):
        """Remove stale connections"""
        now = datetime.now()
        stale_connections = []
        
        async with self._lock:
            for connection in self.connections.values():
                if (now - connection.created_at) > max_age and not connection.is_connected:
                    stale_connections.append(connection.connection_id)
        
        for connection_id in stale_connections:
            connection = await self.remove_connection(connection_id)
            if connection:
                await connection.close(code=1001, reason="Stale connection cleanup")
        
        if stale_connections:
            logger.info(f"Cleaned up {len(stale_connections)} stale connections")

class JambonzWebSocketManager:
    """
    Production-ready WebSocket manager for Jambonz integration.
    Handles multiple concurrent calls with proper connection lifecycle management.
    """

    def __init__(self,
                 host: str = "0.0.0.0",
                 port: int = 8765,
                 max_connections: int = 1000,
                 heartbeat_interval: float = 30.0,
                 cleanup_interval: float = 300.0):
        """
        Initialize WebSocket manager

        Args:
            host: Host to bind WebSocket server
            port: Port to bind WebSocket server
            max_connections: Maximum concurrent connections
            heartbeat_interval: Heartbeat interval in seconds
            cleanup_interval: Connection cleanup interval in seconds
        """
        self.host = host
        self.port = port
        self.heartbeat_interval = heartbeat_interval
        self.cleanup_interval = cleanup_interval

        # Core components
        self.connection_pool = ConnectionPool(max_connections)
        self.reconnection_manager = ReconnectionManager()

        # Server state
        self.server = None
        self.is_running = False
        self.server_start_time: Optional[datetime] = None
        self._shutdown_event = asyncio.Event()

        # Background tasks
        self._heartbeat_task = None
        self._cleanup_task = None

        # Message handlers (to be set by application)
        self.message_handlers: Dict[str, Callable] = {}

        # Metrics
        self.total_connections = 0
        self.active_calls = 0

    def register_message_handler(self, message_type: str, handler: Callable):
        """
        Register handler for specific message type

        Args:
            message_type: Type of message (e.g., 'session:new', 'verb:hook')
            handler: Async handler function
        """
        self.message_handlers[message_type] = handler
        logger.info(f"Registered handler for message type: {message_type}")

    async def _process_request(self, connection, request):
        """
        Process HTTP requests before WebSocket upgrade.
        Handle CORS preflight and other HTTP requests.

        Args:
            connection: The WebSocket connection
            request: The HTTP request object

        Returns:
            Response object for HTTP responses or None to proceed with WebSocket upgrade
        """
        # Extract path and headers from request object
        path = request.path
        request_headers = request.headers

        # Get request method from headers (websockets doesn't provide it directly)
        # For CORS preflight, we need to handle OPTIONS requests
        origin = request_headers.get("Origin")

        # Handle CORS preflight requests (OPTIONS method)
        # We detect this by checking for Access-Control-Request-Method header
        if "Access-Control-Request-Method" in request_headers:
            logger.info(f"Handling CORS preflight request for path: {path}")

            # Create response using connection.respond()
            response = connection.respond(http.HTTPStatus.OK, "")

            # Add CORS headers
            if origin:
                response.headers["Access-Control-Allow-Origin"] = origin
            else:
                response.headers["Access-Control-Allow-Origin"] = "*"

            response.headers["Access-Control-Allow-Methods"] = "GET, POST, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization, Sec-WebSocket-Protocol, Sec-WebSocket-Key, Sec-WebSocket-Version, Upgrade, Connection"
            response.headers["Access-Control-Max-Age"] = "86400"  # 24 hours
            response.headers["Vary"] = "Origin"

            return response

        # Handle health check endpoint
        if path == "/health" or path == "/health/":
            logger.debug(f"Health check request for path: {path}")

            # Get server stats for health check
            health_data = {
                "status": "healthy",
                "service": "jambonz-websocket-server",
                "timestamp": datetime.now().isoformat(),
                "uptime_seconds": (datetime.now() - self.server_start_time).total_seconds() if hasattr(self, 'server_start_time') else 0,
                "active_connections": len(self.connection_pool.connections),
                "server_info": {
                    "host": self.host,
                    "port": self.port,
                    "protocol": "ws.jambonz.org"
                }
            }

            health_json = json.dumps(health_data, indent=2)
            response = connection.respond(http.HTTPStatus.OK, health_json)

            # Add CORS headers
            if origin:
                response.headers["Access-Control-Allow-Origin"] = origin
            else:
                response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Content-Type"] = "application/json"

            return response

        # Handle regular HTTP requests to WebSocket endpoints
        # Only reject if it's NOT a WebSocket upgrade request
        if path.startswith("/ws/"):
            # Check if this is a WebSocket upgrade request
            upgrade_header = request_headers.get("Upgrade", "").lower()
            connection_header = request_headers.get("Connection", "").lower()

            # If it's a WebSocket upgrade request, allow it to proceed
            if upgrade_header == "websocket" and "upgrade" in connection_header:
                logger.debug(f"WebSocket upgrade request for path: {path}")
                return None  # Allow WebSocket upgrade to proceed

            # Otherwise, it's a regular HTTP request to a WebSocket endpoint
            logger.info(f"HTTP request to WebSocket endpoint: {path}")

            # Return informational response
            error_data = {
                "message": "This is a WebSocket endpoint. Use WebSocket protocol to connect.",
                "protocol": "ws.jambonz.org",
                "path": path,
                "timestamp": datetime.now().isoformat()
            }

            error_json = json.dumps(error_data)
            response = connection.respond(http.HTTPStatus.BAD_REQUEST, error_json)

            # Add CORS headers
            if origin:
                response.headers["Access-Control-Allow-Origin"] = origin
            else:
                response.headers["Access-Control-Allow-Origin"] = "*"
            response.headers["Content-Type"] = "application/json"

            return response

        # For other paths, return 404
        error_data = {
            "error": "Not Found",
            "message": f"Path {path} not found",
            "timestamp": datetime.now().isoformat()
        }

        error_json = json.dumps(error_data)
        response = connection.respond(http.HTTPStatus.NOT_FOUND, error_json)

        if origin:
            response.headers["Access-Control-Allow-Origin"] = origin
        response.headers["Content-Type"] = "application/json"

        return response

    async def start_server(self):
        """Start WebSocket server and background tasks"""
        if self.is_running:
            logger.warning("WebSocket server is already running")
            return

        try:
            # Create wrapper function to handle websockets library API variations
            # Different versions of websockets library may call handler differently
            async def connection_handler(websocket, path=None):
                """Handle websocket connection with flexible parameter handling"""
                # Try to get path from parameter first, then from websocket.path attribute
                if path is None:
                    path = getattr(websocket, 'path', None)

                # If still no path, try to get it from the request URI
                if not path:
                    # For websockets.connect() connections, the path might not be available
                    # In this case, we'll extract it from the remote address or use a default
                    remote_addr = getattr(websocket, 'remote_address', ('unknown', 0))
                    logger.warning(f"No path available in WebSocket connection from {remote_addr}")

                    # No path available - this should not happen with proper tunneling
                    logger.error(f"No path available for connection from {remote_addr}")
                    await websocket.close(code=1002, reason="Invalid connection path")
                    return

                logger.info(f"WebSocket connection received with path: {path}")
                await self._handle_connection(websocket, path)

            # Start WebSocket server with Jambonz subprotocol and HTTP request processing
            self.server = await websockets.serve(
                connection_handler,
                self.host,
                self.port,
                subprotocols=["ws.jambonz.org"],
                ping_interval=self.heartbeat_interval,
                ping_timeout=self.heartbeat_interval * 2,
                max_size=1024 * 1024,  # 1MB max message size
                max_queue=100,  # Max queued messages per connection
                compression=None,  # Disable compression for lower latency
                process_request=self._process_request  # Handle HTTP requests
            )

            self.is_running = True
            self.server_start_time = datetime.now()

            # Start background tasks
            self._heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())

            logger.info(f"WebSocket server started on {self.host}:{self.port}")
            logger.info(f"Subprotocol: ws.jambonz.org")
            logger.info(f"Max connections: {self.connection_pool.max_connections}")

        except Exception as e:
            logger.error(f"Failed to start WebSocket server: {e}")
            raise

    async def stop_server(self):
        """Stop WebSocket server and cleanup resources"""
        if not self.is_running:
            return

        logger.info("Stopping WebSocket server...")
        self.is_running = False
        self._shutdown_event.set()

        # Cancel background tasks
        if self._heartbeat_task:
            self._heartbeat_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()

        # Close all connections
        connections = await self.connection_pool.get_all_connections()
        close_tasks = []
        for connection in connections:
            close_tasks.append(connection.close(code=1001, reason="Server shutdown"))

        if close_tasks:
            await asyncio.gather(*close_tasks, return_exceptions=True)

        # Stop server
        if self.server:
            self.server.close()
            await self.server.wait_closed()

        logger.info("WebSocket server stopped")

    def _extract_hospital_id_from_path(self, path: str) -> Optional[str]:
        """
        Extract hospital ID from WebSocket path
        Expected format: /ws/jambonz/{hospital_id}

        Args:
            path: WebSocket connection path

        Returns:
            Hospital ID or None if not found
        """
        try:
            parts = path.strip('/').split('/')
            if len(parts) >= 3 and parts[0] == 'ws' and parts[1] == 'jambonz':
                hospital_id = parts[2]
                logger.debug(f"Extracted hospital ID '{hospital_id}' from path '{path}'")
                return hospital_id
            else:
                logger.warning(f"Path '{path}' does not match expected format '/ws/jambonz/{{hospital_id}}'")
        except Exception as e:
            logger.error(f"Error extracting hospital ID from path {path}: {e}")

        return None

    async def _handle_connection(self, websocket, path):
        """
        Handle new WebSocket connection from Jambonz

        Args:
            websocket: WebSocket connection
            path: Connection path
        """
        connection_id = str(uuid.uuid4())

        # Extract hospital ID from WebSocket path
        hospital_id = self._extract_hospital_id_from_path(path)

        # Validate hospital ID extraction
        if not hospital_id:
            logger.error(f"Could not extract hospital ID from path: {path}. Path must be in format '/ws/jambonz/{{hospital_id}}'")
            await websocket.close(code=1002, reason="Invalid hospital ID in path")
            return

        connection = WebSocketConnection(
            connection_id=connection_id,
            websocket=websocket,
            hospital_id=hospital_id,
            state=ConnectionState.CONNECTING
        )

        logger.info(f"New WebSocket connection: {connection_id} from {websocket.remote_address} for hospital: {hospital_id}")

        try:
            # Add to connection pool
            if not await self.connection_pool.add_connection(connection):
                await websocket.close(code=1013, reason="Server overloaded")
                return

            connection.state = ConnectionState.CONNECTED
            connection.metrics.connected_at = datetime.now()
            self.total_connections += 1

            # Handle messages
            await self._message_loop(connection)

        except ConnectionClosed:
            logger.info(f"Connection {connection_id} closed by client")
        except WebSocketException as e:
            logger.error(f"WebSocket error on connection {connection_id}: {e}")
        except Exception as e:
            logger.error(f"Unexpected error on connection {connection_id}: {e}")
        finally:
            # Cleanup connection
            await self.connection_pool.remove_connection(connection_id)
            if connection.call_sid:
                self.active_calls = max(0, self.active_calls - 1)

    async def _message_loop(self, connection: WebSocketConnection):
        """
        Main message processing loop for a connection

        Args:
            connection: WebSocket connection
        """
        try:
            async for message in connection.websocket:
                if not self.is_running:
                    break

                try:
                    # Parse message
                    data = json.loads(message)
                    message_type = data.get("type")
                    message_id = data.get("msgid")

                    connection.metrics.update_message_received()
                    logger.debug(f"Received message on {connection.connection_id}: {message_type}")

                    # Handle message
                    await self._process_message(connection, data)

                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON from connection {connection.connection_id}: {e}")
                    await self._send_error_response(connection, "Invalid JSON format")
                except Exception as e:
                    logger.error(f"Error processing message on {connection.connection_id}: {e}")
                    await self._send_error_response(connection, "Message processing error")

        except ConnectionClosed:
            logger.info(f"Connection {connection.connection_id} closed during message loop")
        except Exception as e:
            logger.error(f"Error in message loop for {connection.connection_id}: {e}")

    async def _process_message(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """
        Process incoming message from Jambonz

        Args:
            connection: WebSocket connection
            data: Message data
        """
        message_type = data.get("type")
        message_id = data.get("msgid")

        # Update connection metadata based on message
        if message_type == "session:new":
            call_sid = data.get("call_sid")
            if call_sid:
                connection.call_sid = call_sid
                connection.metrics.call_sid = call_sid
                self.active_calls += 1

        # Find and execute handler
        handler = self.message_handlers.get(message_type)
        if handler:
            try:
                response = await handler(connection, data)
                if response and message_id:
                    # Send response with message ID correlation
                    response["msgid"] = message_id
                    await connection.send_message(response)
            except Exception as e:
                logger.error(f"Handler error for {message_type}: {e}")
                await self._send_error_response(connection, f"Handler error: {str(e)}")
        else:
            logger.warning(f"No handler registered for message type: {message_type}")
            await self._send_error_response(connection, f"Unknown message type: {message_type}")

    async def _send_error_response(self, connection: WebSocketConnection, error_message: str):
        """Send error response to Jambonz"""
        error_response = {
            "type": "jambonz:error",
            "error": error_message,
            "timestamp": datetime.now().isoformat()
        }
        await connection.send_message(error_response)

    async def _heartbeat_loop(self):
        """Background task for connection health monitoring"""
        while self.is_running:
            try:
                connections = await self.connection_pool.get_all_connections()
                now = datetime.now()

                for connection in connections:
                    if connection.is_connected:
                        # Update heartbeat
                        connection.last_heartbeat = now

                        # Check for stale connections (no activity for too long)
                        if (connection.metrics.last_message_at and
                            (now - connection.metrics.last_message_at).total_seconds() > 300):  # 5 minutes
                            logger.warning(f"Stale connection detected: {connection.connection_id}")
                            await connection.close(code=1001, reason="Stale connection")

                await asyncio.sleep(self.heartbeat_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in heartbeat loop: {e}")
                await asyncio.sleep(self.heartbeat_interval)

    async def _cleanup_loop(self):
        """Background task for periodic cleanup"""
        while self.is_running:
            try:
                await self.connection_pool.cleanup_stale_connections()
                await asyncio.sleep(self.cleanup_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in cleanup loop: {e}")
                await asyncio.sleep(self.cleanup_interval)

    async def send_message_to_call(self, call_sid: str, message: Dict[str, Any]) -> bool:
        """
        Send message to specific call

        Args:
            call_sid: Call SID
            message: Message to send

        Returns:
            bool: Success status
        """
        connection = await self.connection_pool.get_connection_by_call(call_sid)
        if connection and connection.is_connected:
            return await connection.send_message(message)

        logger.warning(f"No active connection found for call: {call_sid}")
        return False

    async def broadcast_to_hospital(self, hospital_id: str, message: Dict[str, Any]) -> int:
        """
        Broadcast message to all connections for a hospital

        Args:
            hospital_id: Hospital ID
            message: Message to broadcast

        Returns:
            int: Number of successful sends
        """
        connections = await self.connection_pool.get_hospital_connections(hospital_id)
        success_count = 0

        send_tasks = []
        for connection in connections:
            if connection.is_connected:
                send_tasks.append(connection.send_message(message))

        if send_tasks:
            results = await asyncio.gather(*send_tasks, return_exceptions=True)
            success_count = sum(1 for result in results if result is True)

        return success_count

    async def get_connection_metrics(self) -> Dict[str, Any]:
        """Get comprehensive connection metrics"""
        connections = await self.connection_pool.get_all_connections()

        total_connections = len(connections)
        active_connections = sum(1 for conn in connections if conn.is_connected)

        # Calculate average metrics
        total_messages_sent = sum(conn.metrics.messages_sent for conn in connections)
        total_messages_received = sum(conn.metrics.messages_received for conn in connections)

        # Group by hospital
        hospital_stats = {}
        for connection in connections:
            if connection.hospital_id:
                if connection.hospital_id not in hospital_stats:
                    hospital_stats[connection.hospital_id] = {
                        "connections": 0,
                        "active_calls": 0,
                        "messages_sent": 0,
                        "messages_received": 0
                    }

                stats = hospital_stats[connection.hospital_id]
                stats["connections"] += 1
                if connection.call_sid:
                    stats["active_calls"] += 1
                stats["messages_sent"] += connection.metrics.messages_sent
                stats["messages_received"] += connection.metrics.messages_received

        return {
            "server_status": "running" if self.is_running else "stopped",
            "total_connections": total_connections,
            "active_connections": active_connections,
            "active_calls": self.active_calls,
            "total_messages_sent": total_messages_sent,
            "total_messages_received": total_messages_received,
            "hospital_stats": hospital_stats,
            "uptime_seconds": (datetime.now() - self.server_start_time).total_seconds() if self.is_running and self.server_start_time else 0
        }

    @asynccontextmanager
    async def lifespan(self):
        """Context manager for server lifecycle"""
        try:
            await self.start_server()
            yield self
        finally:
            await self.stop_server()

# Global instance for application use
websocket_manager = JambonzWebSocketManager(
    host=os.environ.get("WEBSOCKET_HOST", "0.0.0.0"),
    port=int(os.environ.get("WEBSOCKET_PORT", "8765")),
    max_connections=int(os.environ.get("MAX_WEBSOCKET_CONNECTIONS", "1000")),
    heartbeat_interval=float(os.environ.get("WEBSOCKET_HEARTBEAT_INTERVAL", "30.0")),
    cleanup_interval=float(os.environ.get("WEBSOCKET_CLEANUP_INTERVAL", "300.0"))
)

if __name__ == "__main__":
    """Test the WebSocket server directly"""
    import signal

    async def main():
        """Main function to run WebSocket server"""
        logger.info("Starting WebSocket server for testing...")

        # Set up signal handlers for graceful shutdown
        def signal_handler():
            logger.info("Shutdown signal received.")

        # Start the server
        async with websocket_manager.lifespan():
            logger.info(f"WebSocket server running on {websocket_manager.host}:{websocket_manager.port}")
            logger.info("Press Ctrl+C to stop the server")

            try:
                # Keep the server running
                while True:
                    await asyncio.sleep(1)
            except KeyboardInterrupt:
                logger.info("Shutdown signal received.")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Process interrupted by user.")
