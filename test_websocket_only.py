#!/usr/bin/env python3
"""
Test WebSocket connection only to verify the fix works.
"""

import asyncio
import websockets
import json
import logging
from datetime import datetime

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

async def test_websocket_connection():
    """Test WebSocket connection to verify handshake works"""
    print("Testing WebSocket connection...")
    
    uri = "ws://localhost:8765/ws/jambonz/123"
    
    try:
        # Test WebSocket connection with Jambonz subprotocol
        print(f"Connecting to: {uri}")
        logger.info(f"Attempting WebSocket connection to {uri}")
        async with websockets.connect(
            uri,
            subprotocols=["ws.jambonz.org"]
        ) as websocket:
            print("✅ WebSocket connection successful!")
            print(f"Selected subprotocol: {websocket.subprotocol}")
            
            # Send a test message
            test_message = {
                "type": "test",
                "msgid": "test-123",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("✅ Test message sent")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"✅ Received response: {response}")
            except asyncio.TimeoutError:
                print("⚠️  No response received (timeout - this is expected for test)")
            
    except Exception as e:
        print(f"❌ WebSocket connection error: {e}")
        print(f"Error type: {type(e).__name__}")

async def main():
    """Main test function"""
    print("WebSocket Connection Test")
    print("=" * 30)
    
    await test_websocket_connection()
    
    print("\nTest completed!")

if __name__ == "__main__":
    asyncio.run(main())
