#!/usr/bin/env python3
"""
Test WebSocket proxy connection to verify the fix works.
This tests the complete flow: Jambonz -> FastAPI (8000) -> WebSocket Server (8765)
"""

import asyncio
import websockets
import json
import logging
import uuid
from datetime import datetime

# Enable debug logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_websocket_proxy():
    """Test WebSocket connection through FastAPI proxy"""
    print("Testing WebSocket connection through FastAPI proxy...")
    
    # Connect to FastAPI proxy endpoint (port 8000)
    uri = "ws://localhost:8000/ws/jambonz/123"
    
    try:
        print(f"Connecting to: {uri}")
        async with websockets.connect(
            uri,
            subprotocols=["ws.jambonz.org"]
        ) as websocket:
            print("✅ WebSocket proxy connection successful!")
            print(f"Selected subprotocol: {websocket.subprotocol}")
            
            # Send a test message (Jambonz format)
            test_message = {
                "type": "session:new",
                "msgid": str(uuid.uuid4()),
                "timestamp": datetime.now().isoformat(),
                "data": {
                    "call_sid": "test-call-123",
                    "from": "+1234567890",
                    "to": "+0987654321"
                }
            }
            
            await websocket.send(json.dumps(test_message))
            print("✅ Test message sent")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"✅ Received response: {response}")
            except asyncio.TimeoutError:
                print("⚠️  No response received (timeout - this might be expected)")
            
    except Exception as e:
        print(f"❌ WebSocket proxy connection error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False
    
    return True

async def test_direct_websocket():
    """Test direct WebSocket connection to internal server"""
    print("\nTesting direct WebSocket connection to internal server...")
    
    # Connect directly to WebSocket server (port 8765)
    uri = "ws://localhost:8765/ws/jambonz/123"
    
    try:
        print(f"Connecting to: {uri}")
        async with websockets.connect(
            uri,
            subprotocols=["ws.jambonz.org"]
        ) as websocket:
            print("✅ Direct WebSocket connection successful!")
            print(f"Selected subprotocol: {websocket.subprotocol}")
            
            # Send a test message
            test_message = {
                "type": "test",
                "msgid": "direct-test-123",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("✅ Test message sent")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                print(f"✅ Received response: {response}")
            except asyncio.TimeoutError:
                print("⚠️  No response received (timeout - this might be expected)")
            
    except Exception as e:
        print(f"❌ Direct WebSocket connection error: {e}")
        print(f"Error type: {type(e).__name__}")
        return False
    
    return True

async def main():
    """Main test function"""
    print("WebSocket Proxy Connection Test")
    print("=" * 40)
    

    
    # Test direct connection first
    direct_success = await test_direct_websocket()
    
    # Test proxy connection
    proxy_success = await test_websocket_proxy()
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"Direct WebSocket: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"Proxy WebSocket:  {'✅ PASS' if proxy_success else '❌ FAIL'}")
    
    if direct_success and proxy_success:
        print("\n🎉 All tests passed! WebSocket servers are working correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the error messages above.")

if __name__ == "__main__":
    asyncio.run(main())
