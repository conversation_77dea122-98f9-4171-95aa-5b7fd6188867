#!/usr/bin/env python3
"""
Test script to verify WebSocket server response format fix.
Tests both HTTP requests and WebSocket connections to ensure the fix works.
"""

import asyncio
import websockets
import json
import aiohttp
from datetime import datetime

async def test_http_requests():
    """Test HTTP requests to WebSocket server endpoints"""
    print("Testing HTTP requests...")
    
    base_url = "http://localhost:8765"
    
    async with aiohttp.ClientSession() as session:
        # Test health check endpoint
        try:
            async with session.get(f"{base_url}/health") as response:
                print(f"Health check: {response.status}")
                if response.status == 200:
                    data = await response.json()
                    print(f"Health data: {data.get('status')}")
                else:
                    print(f"Health check failed: {await response.text()}")
        except Exception as e:
            print(f"Health check error: {e}")
        
        # Test WebSocket endpoint with HTTP request
        try:
            async with session.get(f"{base_url}/ws/jambonz/123") as response:
                print(f"WebSocket endpoint HTTP: {response.status}")
                if response.status == 400:
                    data = await response.json()
                    print(f"Expected 400 response: {data.get('message')}")
                else:
                    print(f"Unexpected response: {await response.text()}")
        except Exception as e:
            print(f"WebSocket endpoint HTTP error: {e}")
        
        # Test CORS preflight
        try:
            headers = {
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "GET",
                "Access-Control-Request-Headers": "Content-Type"
            }
            async with session.options(f"{base_url}/ws/jambonz/123", headers=headers) as response:
                print(f"CORS preflight: {response.status}")
                if response.status == 200:
                    print("CORS preflight successful")
                    cors_headers = dict(response.headers)
                    print(f"CORS headers: {cors_headers.get('Access-Control-Allow-Origin')}")
                else:
                    print(f"CORS preflight failed: {await response.text()}")
        except Exception as e:
            print(f"CORS preflight error: {e}")

async def test_websocket_connection():
    """Test WebSocket connection to verify handshake works"""
    print("\nTesting WebSocket connection...")
    
    uri = "ws://localhost:8765/ws/jambonz/123"
    
    try:
        # Test WebSocket connection with Jambonz subprotocol
        async with websockets.connect(
            uri,
            subprotocols=["ws.jambonz.org"],
            extra_headers={
                "Origin": "http://localhost:3000"
            }
        ) as websocket:
            print("WebSocket connection successful!")
            print(f"Selected subprotocol: {websocket.subprotocol}")
            
            # Send a test message
            test_message = {
                "type": "test",
                "msgid": "test-123",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("Test message sent")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"Received response: {response}")
            except asyncio.TimeoutError:
                print("No response received (timeout)")
            
    except websockets.exceptions.InvalidStatusCode as e:
        print(f"WebSocket connection failed with status: {e.status_code}")
        print(f"Response headers: {e.response_headers}")
    except Exception as e:
        print(f"WebSocket connection error: {e}")

async def main():
    """Main test function"""
    print("WebSocket Server Response Format Fix Test")
    print("=" * 50)
    
    # Test HTTP requests first
    await test_http_requests()
    
    # Test WebSocket connection
    await test_websocket_connection()
    
    print("\nTest completed!")

if __name__ == "__main__":
    asyncio.run(main())
