#!/usr/bin/env python3
"""
Simple test to verify WebSocket server response format fix.
"""

import asyncio
import websockets
import json
import requests
from datetime import datetime

def test_http_requests():
    """Test HTTP requests to WebSocket server endpoints"""
    print("Testing HTTP requests...")
    
    base_url = "http://localhost:8765"
    
    # Test health check endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health check: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Health data: {data.get('status')}")
        else:
            print(f"Health check failed: {response.text}")
    except Exception as e:
        print(f"Health check error: {e}")
    
    # Test WebSocket endpoint with HTTP request
    try:
        response = requests.get(f"{base_url}/ws/jambonz/123", timeout=5)
        print(f"WebSocket endpoint HTTP: {response.status_code}")
        if response.status_code == 400:
            data = response.json()
            print(f"Expected 400 response: {data.get('message')}")
        else:
            print(f"Unexpected response: {response.text}")
    except Exception as e:
        print(f"WebSocket endpoint HTTP error: {e}")
    
    # Test CORS preflight
    try:
        headers = {
            "Origin": "http://localhost:3000",
            "Access-Control-Request-Method": "GET",
            "Access-Control-Request-Headers": "Content-Type"
        }
        response = requests.options(f"{base_url}/ws/jambonz/123", headers=headers, timeout=5)
        print(f"CORS preflight: {response.status_code}")
        if response.status_code == 200:
            print("CORS preflight successful")
            print(f"CORS headers: {response.headers.get('Access-Control-Allow-Origin')}")
        else:
            print(f"CORS preflight failed: {response.text}")
    except Exception as e:
        print(f"CORS preflight error: {e}")

async def test_websocket_connection():
    """Test WebSocket connection to verify handshake works"""
    print("\nTesting WebSocket connection...")
    
    uri = "ws://localhost:8765/ws/jambonz/123"
    
    try:
        # Test WebSocket connection with Jambonz subprotocol
        async with websockets.connect(
            uri,
            subprotocols=["ws.jambonz.org"]
        ) as websocket:
            print("WebSocket connection successful!")
            print(f"Selected subprotocol: {websocket.subprotocol}")
            
            # Send a test message
            test_message = {
                "type": "test",
                "msgid": "test-123",
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(test_message))
            print("Test message sent")
            
            # Wait for response (with timeout)
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                print(f"Received response: {response}")
            except asyncio.TimeoutError:
                print("No response received (timeout - this is expected)")
            
    except Exception as e:
        print(f"WebSocket connection error: {e}")

def main():
    """Main test function"""
    print("WebSocket Server Response Format Fix Test")
    print("=" * 50)
    
    # Test HTTP requests first
    test_http_requests()
    
    # Test WebSocket connection
    asyncio.run(test_websocket_connection())
    
    print("\nTest completed!")

if __name__ == "__main__":
    main()
